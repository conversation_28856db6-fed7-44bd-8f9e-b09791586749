"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Co<PERSON>, <PERSON>ader2, <PERSON><PERSON><PERSON>, Trash2, Heart, Brain, Zap } from "lucide-react";
import { toast } from "sonner";
import { useTranslations } from "next-intl";
import { useAppContext } from "@/contexts/app";
import FavoriteModal from "@/components/ui/favorite-modal";
import { PromptCategory } from "@/types/prompt-category";

export default function PromptGenerator() {
  const {user, setShowSignModal} = useAppContext();
  const t = useTranslations("prompt_generator");
  const [input, setInput] = useState("");
  const [output, setOutput] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [showFavoriteModal, setShowFavoriteModal] = useState(false);
  const [categories, setCategories] = useState<PromptCategory[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [mode, setMode] = useState<"basic" | "advanced">("basic");

  // 监听用户登录状态，恢复临时保存的内容
  useEffect(() => {
    if (user) {
      const tempOutput = localStorage.getItem('temp_prompt_output');
      if (tempOutput) {
        setOutput(tempOutput);
        localStorage.removeItem('temp_prompt_output');
        // 自动打开收藏模态框
        setShowFavoriteModal(true);
      }
      // 登录后默认切换到高级模式
      setMode("advanced");
    } else {
      // 未登录时默认基础模式
      setMode("basic");
    }
  }, [user]);

  const requestGenText = async (input: string, mode: string, locale: string) => {
    const response = await fetch("/api/generate/gen-text", {
      method: "POST",
      body: JSON.stringify({input, mode, locale}),
    });
    return response.json();
  };

  const handleGenerate = async () => {

    if (!input.trim()) {
      toast.error(t("error_no_input"));
      return;
    }

    setIsGenerating(true);
    
    try {
      const locale = document.documentElement.lang || "en";
      console.log(input);
      const response = await requestGenText(input, mode, locale);
      console.log(response);
      if (response.code === -1) {
        toast.error(response.message);
        return;
      }

      setOutput(response.data.text);
      toast.success(t("success_generated"));
    } catch (error) {
      console.error(error);
      toast.error(t("error_generate_failed"));
    } finally {
      setIsGenerating(false);
    }
  };

  const handleCopyOutput = async () => {
    if (!output) {
      toast.error(t("error_no_content"));
      return;
    }

    try {
      await navigator.clipboard.writeText(output);
      toast.success(t("success_copied"));
    } catch (error) {
      toast.error(t("error_copy_failed"));
    }
  };

  const handleClearOutput = () => {
    setOutput("");
  };

  const handleClearInput = () => {
    setInput("");
  };

  const handleModeChange = (newMode: "basic" | "advanced") => {
    // 如果用户未登录且尝试选择高级模式，弹出登录模态框
    if (!user && newMode === "advanced") {
      setShowSignModal(true);
      return;
    }

    setMode(newMode);
  };

  const handleFavorite = async () => {
    console.log('点击收藏按钮，用户状态:', user ? '已登录' : '未登录');
    console.log('当前输出内容:', output);
    
    // 检查用户登录状态
    if (!user) {
      // 保存当前输出内容到 localStorage
      localStorage.setItem('temp_prompt_output', output);
      setShowSignModal(true);
      return;
    }
    
    // 用户已登录，获取分类并打开收藏模态框
    console.log('开始获取分类并打开模态框...');
    await fetchCategories();
    console.log('分类获取完成，当前categories:', categories);
    setShowFavoriteModal(true);
    console.log('模态框已设置为显示');
  };

  const fetchCategories = async () => {
    setIsLoadingCategories(true);
    try {
      const response = await fetch('/api/prompt/categories');
      
      const data = await response.json();
      console.log('分类API原始响应数据:', data);
      
      if (data.code === 0) {
        const categoriesData = data.data.categories || [];
        setCategories(categoriesData);
      } else {
        toast.error(t("error_get_categories_failed_with_msg", { message: data.message || '未知错误' }));
      }
    } catch (error) {
      console.error('获取分类异常:', error);
      toast.error(t("error_get_categories_failed"));
    } finally {
      setIsLoadingCategories(false);
    }
  };

  const createNewCategory = async (name: string) => {
    try {
      const response = await fetch('/api/prompt/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: name.trim() }),
      });

      const data = await response.json();
      if (data.code === 0) {
        return data.data.uuid;
      } else {
        // 根据错误类型显示不同的提示
        if (data.message.includes("already exists")) {
          throw new Error(`category "${name.trim()}" already exists, please use another name`);
        } else {
          throw new Error(data.message || t("error_favorite_failed"));
        }
      }
    } catch (error) {
      throw error;
    }
  };

  const handleFavoriteSave = async (data: {
    title: string;
    content: string;
    notes: string;
    categoryUuid: string | null;
    isNewCategory: boolean;
    newCategoryName: string;
  }) => {
    try {
      // FavoriteModal 已经处理了分类创建，这里直接使用传入的 categoryUuid
      let categoryUuid = data.categoryUuid;

      const response = await fetch('/api/prompt/favorites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: data.title,
          prompt: data.content,
          category_uuid: categoryUuid || "",
          notes: data.notes || "",
        }),
      });

      const result = await response.json();
      console.log(result);
      if (result.code === 0) {
        toast.success(t("success_favorite_saved"));
        // 成功后会自动关闭模态框
      } else {
        toast.error(result.message || t("error_favorite_failed"));
        throw new Error(result.message || t("error_favorite_failed"));
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : t("error_favorite_failed"));
      throw error;
    }
  };

  return (
    <section id="generator">
      {/* <div className="container"> */}
        <Card className="w-full max-w-7xl mx-auto">
          <CardHeader className="text-center">
            <CardTitle className="flex items-center justify-center gap-2 text-2xl">
              <Sparkles className="h-6 w-6 text-primary" />
              {t("title")}
            </CardTitle>
            <p className="text-muted-foreground">
              {t("subtitle")}
            </p>
          </CardHeader>
          
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 左侧输入区域 */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="input" className="text-base font-medium">
                    {t("input_label")}
                  </Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClearInput}
                    className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                
                <Separator />

                <Textarea
                  id="input"
                  placeholder={t("input_placeholder")}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  className="min-h-[250px] resize-none"
                />

                {/* 模式选择按钮组 */}
                <div className="space-y-4">
                  {/* 模式选择和提示文案组 */}
                  <div className="space-y-2">
                    {/* 第一行：模式选择 */}
                    <div className="grid grid-cols-2 gap-3">
                      <Button
                        variant={mode === "basic" ? "default" : "outline"}
                        size="lg"
                        onClick={() => handleModeChange("basic")}
                        className={`
                          flex items-center justify-center gap-2 transition-all duration-300
                          ${mode === "basic"
                            ? "bg-blue-500 hover:bg-blue-600 text-white border-blue-500 shadow-md"
                            : "bg-gray-50 text-gray-700 border-gray-300 hover:bg-gray-100"
                          }
                        `}
                      >
                        <Zap className="h-4 w-4" />
                        {t("mode_basic")}
                      </Button>

                      <Button
                        variant={mode === "advanced" ? "default" : "outline"}
                        size="lg"
                        onClick={() => handleModeChange("advanced")}
                        className={`
                          flex items-center justify-center gap-2 transition-all duration-300
                          ${mode === "advanced"
                            ? "bg-gradient-to-r from-purple-500 to-purple-600 text-white border-purple-500 hover:from-purple-600 hover:to-purple-700 shadow-lg"
                            : "bg-gray-50 text-gray-700 border-gray-300 hover:bg-gray-100"
                          }
                          ${!user ? "hover:bg-gradient-to-r hover:from-purple-50 hover:to-purple-100 hover:border-purple-300 hover:text-purple-700" : ""}
                        `}
                      >
                        <Brain className="h-4 w-4" />
                        {t("mode_advanced")}
                      </Button>
                    </div>

                    {/* 模式提示文案 */}
                    <div className="text-center">
                      {mode === "basic" ? (
                        <p className="text-sm text-gray-600">
                          {t("basic_mode_simple_hint")}
                        </p>
                      ) : (
                        <p className="text-sm text-gray-600">
                          {t("advanced_mode_simple_hint")}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* 第二行：生成按钮 */}
                  <Button
                    onClick={handleGenerate}
                    disabled={isGenerating || !input.trim()}
                    size="lg"
                    className="w-full"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {t("generating")}
                      </>
                    ) : (
                      <>
                        <Sparkles className="mr-2 h-4 w-4" />
                        {t("generate_button")}
                      </>
                    )}
                  </Button>
                </div>
              </div>

              {/* 右侧输出区域 */}
              <div className="space-y-4 flex flex-col h-full">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-medium">
                    {t("output_label")}
                  </Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClearOutput}
                    className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                
                <Separator />
                
                <div className="relative flex-1">
                  <Textarea
                    placeholder={t("output_placeholder")}
                    value={output}
                    onChange={(e) => setOutput(e.target.value)}
                    className="min-h-[250px] resize-none h-full"
                  />


                  {isGenerating && (
                    <div className="absolute inset-0 flex items-center justify-center pointer-events-none bg-background/80">
                      <div className="text-center">
                        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-primary" />
                        <p className="text-sm text-muted-foreground">{t("generating_text")}</p>
                      </div>
                    </div>
                  )}
                </div>

                {/* 操作按钮区域 - 始终显示，与左侧生成按钮对齐 */}
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={handleCopyOutput}
                    disabled={!output}
                    className="flex-1"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    {t("copy_button")}
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    onClick={handleFavorite}
                    disabled={!output}
                    className="flex-1"
                  >
                    <Heart className="h-4 w-4 mr-2" />
                    {t("favorite_button")}
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>

          {/* 收藏模态框 */}
          <FavoriteModal
            open={showFavoriteModal}
            onOpenChange={setShowFavoriteModal}
            mode="create"
            initialData={{
              content: output,
            }}
            categories={categories}
            isLoadingCategories={isLoadingCategories}
            onSave={handleFavoriteSave}
            onCreateCategory={createNewCategory}
          />
        </Card>
      {/* </div> */}
    </section>
  );
}
