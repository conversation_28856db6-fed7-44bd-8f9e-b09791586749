{"template": "shipany-template-one", "theme": "light", "header": {"brand": {"title": "Prompt Ark", "logo": {"src": "/logo.png", "alt": "Prompt Ark"}, "url": "/"}, "nav": {"items": [{"title": "Generators", "icon": "RiMagicLine", "children": [{"title": "ChatGPT Prompt Generator", "url": "/chatgpt-prompt-generator", "icon": "RiMagicLine"}, {"title": "VEO 3 Prompt Generator", "url": "/veo-3-prompt-generator", "icon": "RiMagicLine"}, {"title": "Veo 3 JSON Prompt Generator", "url": "/veo-3-json-prompt-generator", "icon": "RiMagicLine"}, {"title": "Veo 3 Video Generator", "url": "/veo-3-video-generator", "icon": "RiVideoLine"}, {"title": "<PERSON><PERSON> Prompt", "url": "/lyra-prompt", "icon": "RiMagicLine"}]}, {"title": "Prompt Library", "url": "/prompt-library", "icon": "RiBookLine"}, {"title": "Blog", "url": "/posts", "icon": "RiBookOpenLine"}, {"title": "Pricing", "url": "/pricing", "icon": "RiMoneyCnyCircleLine"}, {"title": "Join <PERSON>", "url": "https://discord.gg/KBT3pX2A", "icon": "RiDiscordLine"}], "buttons": [{"title": "Sign In", "variant": "primary", "href": "/auth/signin"}]}, "show_sign": true, "show_theme": true, "show_locale": true}, "hero": {"title": "AI Prompt Generator - Create Perfect Prompts Instantly", "highlight_text": "AI Prompt Generator", "description": "Generate optimized prompts for ChatGPT, Claude, Gemini & any AI model.<br/> Transform your ideas into powerful prompts with advanced prompt engineering.<br/>Get better AI results every time.", "announcement": {"label": "NEW", "title": "🚀 Free AI Prompt Generator", "url": "/#generator"}, "tip": "🎁 Free prompt generator available", "buttons": [{"title": "Generate Prompts", "icon": "RiMagicLine", "url": "/#generator", "target": "_self", "variant": "default"}, {"title": "Learn More", "icon": "RiBookOpenLine", "url": "/#feature", "target": "_self", "variant": "outline"}], "show_happy_users": false, "show_badge": false}, "branding": {"title": "AI Prompt Generator Powered by Leading AI Technologies", "items": [{"title": "OpenAI", "image": {"src": "/imgs/logos/openai.svg", "alt": "OpenAI"}}, {"title": "<PERSON>", "image": {"src": "/imgs/logos/claude.svg", "alt": "<PERSON>"}}, {"title": "Gemini", "image": {"src": "/imgs/logos/gemini.svg", "alt": "Gemini"}}, {"title": "DeepSeek", "image": {"src": "/imgs/logos/deepseek.svg", "alt": "DeepSeek"}}]}, "tips": {"name": "tips", "title": "AI Prompt Optimization Technology", "subtitle": "Our AI prompt generator integrates the following professional techniques to create optimized, high-quality prompts with one click", "description": "Our AI prompt generator has mastered these prompt writing techniques and can automatically generate the most optimized prompts for your needs, helping AI understand your intentions more accurately", "example_label": "Example", "tips": [{"id": "clarity", "title": "Keep Instructions Clear", "description": "Use specific, clear language to describe your needs, avoiding vague and ambiguous expressions. The more specific your instructions, the better AI understands your true intent.", "icon": "RiFileTextLine", "example": "Write an article about AI → Write a 1000-word article introducing the current applications and future trends of AI in healthcare", "category": "Basic Tips"}, {"id": "context", "title": "Provide Sufficient Context", "description": "Give AI enough background information, including target audience, usage scenarios, expected style and format, helping AI generate content that better fits your needs.", "icon": "RiInformationLine", "example": "You are an experienced product manager writing a product requirements document for a startup...", "category": "Basic Tips"}, {"id": "step-by-step", "title": "Use Step-by-Step Instructions", "description": "Break complex tasks into multiple simple steps, letting AI complete tasks in clear logical order, improving output quality and accuracy.", "icon": "RiListCheck3", "example": "1. Analyze the problem background 2. List possible solutions 3. Evaluate pros and cons of each solution 4. Recommend the best solution", "category": "Advanced Tips"}, {"id": "examples", "title": "Provide Specific Examples", "description": "By providing examples of expected output, help AI understand your format requirements and content style, ensuring generated content meets expectations.", "icon": "RiLightbulbLine", "example": "Please answer in this format: **Question:** [question content] **Answer:** [detailed response] **Summary:** [key points]", "category": "Basic Tips"}, {"id": "role-play", "title": "Define Role and Identity", "description": "Set specific role identities for AI, such as expert, teacher, consultant, etc., letting AI answer questions from corresponding professional perspectives.", "icon": "RiUserLine", "example": "You are a senior data scientist, please analyze this machine learning problem from a professional perspective...", "category": "Advanced Tips"}, {"id": "constraints", "title": "Set Reasonable Constraints", "description": "Clearly specify word limits, format requirements, language style and other constraints, helping AI generate content that better meets requirements.", "icon": "RiSettings3Line", "example": "Please explain what blockchain is in less than 500 words using simple language for elementary school students", "category": "Intermediate Tips"}]}, "introduce": {"name": "introduce", "title": "What is AI Prompt Generator", "label": "Introduce", "description": "Prompt Ark is an advanced AI prompt generator designed for effective prompt engineering. Create better artificial intelligence prompts that deliver superior results.", "image": {"src": "/imgs/features/1.png", "alt": "Prompt Ark - Free AI Prompt Generator"}, "items": [{"title": "Smart Prompt Templates", "description": "Access hundreds of proven prompt templates for different AI models and use cases.", "icon": "RiFileTextLine"}, {"title": "AI-Powered Optimization", "description": "Our prompt generator uses AI to improve your prompts for better results automatically.", "icon": "RiMagicLine"}, {"title": "Multi-Model Support", "description": "Supporting prompt optimization for ChatGPT prompts, <PERSON> prompts, Gemini prompts, DeepSeek prompts, etc.", "icon": "RiRobot2Line"}]}, "benefit": {"name": "benefit", "title": "Why Choose AI Prompt Generator", "label": "Benefits", "description": "Get the most powerful AI prompt generator with advanced prompt engineering features to boost your AI results.", "items": [{"title": "Advanced Prompt Engineering", "description": "Use our AI prompt generator with proven techniques for better AI responses every time.", "icon": "RiToolsLine", "image": {"src": "/imgs/features/2.png", "alt": "Prompt Ark - Advanced Prompt Engineering"}}, {"title": "Instant Results", "description": "Generate optimized artificial intelligence prompts in seconds. No complex setup or learning curve required.", "icon": "RiFlashlightLine", "image": {"src": "/imgs/features/3.png", "alt": "Prompt Ark - Instant Results with AI Prompt Generator"}}, {"title": "Expert Support", "description": "Access our prompt engineering community and get help from AI experts to master prompt creation.", "icon": "RiTeamLine", "image": {"src": "/imgs/features/4.png", "alt": "Prompt Ark - Expert Support for Prompt Engineering"}}]}, "usage": {"name": "usage", "title": "How to Use AI Prompt Generator", "description": "Generate perfect AI prompts in four simple steps:", "image": {"src": "/imgs/features/1.png"}, "image_position": "left", "text_align": "center", "items": [{"title": "Describe Your Task", "description": "Tell our AI prompt generator what you want to achieve with your artificial intelligence prompt.", "image": {"src": "https://r2.promptark.net/write-prompt.gif", "alt": "Describe Your Task with AI Prompt Generator"}}, {"title": "Generate & Optimize", "description": "Our AI prompt generator generates optimized prompts using advanced prompt engineering techniques.", "image": {"src": "/imgs/features/6.png", "alt": "Generate & Optimize with AI Prompt Generator"}}, {"title": "Test & Refine", "description": "Use your generated prompts and refine them further with our prompt generator for better results.", "image": {"src": "/imgs/features/7.png", "alt": "Test & Refine"}}, {"title": "Build Your Prompt Library", "description": "Collect and organize your best-performing prompts in a personal library for easy retrieval and repeated use.", "image": {"src": "/imgs/features/8.png", "alt": "Build Your Prompt Library with Prompt Ark"}}]}, "feature": {"name": "feature", "title": "Key Features of AI Prompt Generator", "description": "Everything you need for effective prompt engineering and AI prompt generation.", "items": [{"title": "AI Prompt Generator", "description": "Generate high-quality prompts for any AI model with our advanced artificial intelligence prompt creator.", "icon": "RiMagicLine"}, {"title": "Prompt Engineering Tools", "description": "Professional prompt engineering features to optimize your AI interactions and get better results.", "icon": "RiToolsLine"}, {"title": "Template Library", "description": "Access hundreds of proven prompt templates for different use cases and AI applications.", "icon": "RiBookOpenLine"}, {"title": "Multi-Model Support", "description": "Our AI prompt generator creates optimized prompts for GPT, Claude, Gemini, LLaMA, DeepSeek, and other popular AI models.", "icon": "RiRobot2Line"}, {"title": "Prompt Library Manager", "description": "Take control of your prompt collection with comprehensive management tools. Edit, organize, and access your saved prompts with ease.", "icon": "RiEyeLine"}, {"title": "Writing Prompt Creator", "description": "Specialized tools for creating writing prompts that inspire creativity and produce better content.", "icon": "RiPenNibLine"}]}, "stats": {"disabled": true, "name": "stats", "label": "Stats", "title": "People Love Prompt Ark", "description": "for its powerful AI prompt generation and easy prompt engineering.", "icon": "FaRegHeart", "items": [{"title": "Trusted by", "label": "10K+", "description": "Users"}, {"title": "Generated", "label": "100K+", "description": "AI Prompts"}, {"title": "Success Rate", "label": "95%", "description": "Better Results"}]}, "pricing": {"name": "pricing", "label": "Pricing", "title": "Choose Your Perfect Plan", "description": "Start free and get 10x productivity boost", "groups": [{"name": "subscribe-yearly", "title": "Yearly (Save 30%)"}, {"name": "subscribe-monthly", "title": "Monthly"}], "items": [{"group": "subscribe-yearly", "title": "Free Plan", "description": "Try before you commit. Ideal for light usage.", "features_title": "What's included", "features": ["Basic prompt generation (worth $3/month)", "Access to community support"], "interval": "year", "amount": 0, "currency": "", "price": "Free", "original_price": "", "unit": "", "is_featured": false, "tip": "🎁 Forever free", "product_id": "", "product_name": "", "credits": 0, "valid_months": 12}, {"group": "subscribe-yearly", "title": "Advanced Plan", "description": "1800 credits/year – great for regular prompt users", "features_title": "All Free Plan features, plus:", "features": ["Advanced prompt generation", "Faster processing queue", "Priority email support"], "interval": "year", "amount": 4192, "currency": "USD", "price": "41.92", "original_price": "59.88", "unit": "USD/year", "is_featured": false, "tip": "💰 Best for creators using prompts regularly", "button": {"title": "Upgrade Now", "url": "/#generator", "icon": "RiMagicLine"}, "product_id": "prod_STkXraIzq5zi8P", "product_name": "Yearly Subscription Advanced", "credits": 1800, "valid_months": 12}, {"group": "subscribe-yearly", "title": "Pro Plan", "description": "6000 credits/year – built for professionals & power users", "features_title": "All Advanced Plan features, plus:", "features": ["API access", "Top priority request processing", "Early access to new features", "Manage personal prompt collections"], "interval": "year", "amount": 8392, "currency": "USD", "price": "83.92", "original_price": "119.88", "unit": "USD/year", "is_featured": true, "tip": "🎖️ Best value for pros – save $36/year", "button": {"title": "Subscribe Now", "url": "/#generator", "icon": "RiMagicLine"}, "product_id": "prod_STkRIN5iI4x3LQ", "product_name": "Yearly Subscription Pro", "credits": 6000, "valid_months": 12}, {"group": "subscribe-monthly", "title": "Free Plan", "description": "Perfect for trying out and light usage", "features_title": "What's included", "features": ["Basic prompt generation (worth $3/month)", "Access to community support"], "interval": "month", "amount": 0, "currency": "", "price": "Free", "original_price": "", "unit": "", "is_featured": false, "tip": "🎁 Forever free", "product_id": "free", "product_name": "", "credits": 0, "valid_months": 1}, {"group": "subscribe-monthly", "title": "Advanced Plan", "description": "150 credits/month – great for moderate usage", "features_title": "All Free Plan features, plus:", "features": ["Advanced prompt generation", "Faster processing queue", "Priority email support"], "interval": "month", "amount": 499, "currency": "USD", "price": "4.99", "unit": "USD/month", "is_featured": false, "tip": "💰 Flexible monthly access", "button": {"title": "Upgrade Now", "url": "/#generator", "icon": "RiMagicLine"}, "product_id": "prod_STkbHMhZ5CunAo", "product_name": "Monthly Subscription Advanced", "credits": 150, "valid_months": 1}, {"group": "subscribe-monthly", "title": "Pro Plan", "description": "500 credits/month – designed for power users", "features_title": "All Advanced Plan features, plus:", "features": ["API access", "Top priority request processing", "Early access to new features", "Manage personal prompt collections"], "interval": "month", "amount": 999, "currency": "USD", "price": "9.99", "unit": "USD/month", "is_featured": true, "tip": "🎖️ Full access, full power", "button": {"title": "Subscribe Now", "url": "/#generator", "icon": "RiMagicLine"}, "product_id": "prod_STkZuduAicQbiT", "product_name": "Monthly Subscription Pro", "credits": 500, "valid_months": 1}]}, "testimonial": {"disabled": true, "name": "testimonial", "label": "Testimonial", "title": "What Users Say About Prompt Ark", "description": "Hear from professionals who improved their AI results with our prompt generator and prompt engineering tools.", "icon": "GoThumbsup", "items": [{"title": "<PERSON>", "label": "Content Creator", "description": "Prompt Ark transformed my AI writing process. The prompt generator creates amazing artificial intelligence prompts that get me exactly the content I need every time.", "image": {"src": "/imgs/users/1.png"}}, {"title": "<PERSON>", "label": "Marketing Manager", "description": "The prompt engineering features are incredible. I can create better prompts for our marketing campaigns and the AI responses are so much more targeted now.", "image": {"src": "/imgs/users/2.png"}}, {"title": "<PERSON>", "label": "Software Developer", "description": "As a developer, I need precise AI responses. This writing prompt creator helps me generate technical prompts that work perfectly with different AI models.", "image": {"src": "/imgs/users/3.png"}}, {"title": "<PERSON>", "label": "Research Analyst", "description": "The template library is amazing. I can quickly find and customize prompts for my research needs. Best AI prompt generator I've ever used!", "image": {"src": "/imgs/users/4.png"}}, {"title": "<PERSON>", "label": "Freelance Writer", "description": "Prompt Ark made prompt engineering so simple. The generated prompts help me create better content faster. My clients love the improved quality.", "image": {"src": "/imgs/users/5.png"}}, {"title": "<PERSON>", "label": "Business Owner", "description": "This prompt generator saved me hours every week. The artificial intelligence prompts work perfectly for my business automation needs.", "image": {"src": "/imgs/users/6.png"}}]}, "faq": {"name": "faq", "label": "FAQ", "title": "Frequently Asked Questions About AI Prompt Generator", "description": "Have another question about Prompt Ark? Contact us(<EMAIL>) for prompt engineering support.", "items": [{"title": "What is an AI prompt generator and how does it work?", "description": "An AI prompt generator is a tool that automatically creates optimized prompts for AI models like ChatGPT, Claude, and Gemini. Our AI prompt generator uses advanced prompt engineering techniques to analyze your input and generate prompts that produce better, more accurate AI responses."}, {"title": "Is this AI prompt generator really free to use?", "description": "Yes! Our AI prompt generator offers a completely free plan that includes basic prompt generation features. You can create and optimize prompts without any cost. We also offer premium plans with advanced prompt engineering tools for power users."}, {"title": "Which AI models work with this prompt generator?", "description": "Our prompt generator supports all major AI models including ChatGPT, Claude (Anthropic), Google Gemini, DeepSeek, LLaMA, and many others. The prompt generator optimizes prompts specifically for each model's unique characteristics and requirements."}, {"title": "How can I create better prompts with this AI prompt generator?", "description": "Simply describe what you want to achieve, and our AI prompt generator will create optimized prompts using proven prompt engineering techniques. The tool automatically structures your prompts, adds context, and includes relevant instructions to maximize AI performance."}, {"title": "What makes this AI prompt generator better than manual prompt writing?", "description": "Our AI prompt generator combines advanced prompt engineering knowledge with AI optimization. It automatically applies best practices like clear instructions, proper formatting, context setting, and result specifications that would take hours to research and implement manually."}, {"title": "Can I save and organize my generated prompts?", "description": "Yes! Our prompt generator includes a built-in prompt library where you can save, organize, and manage your best-performing prompts. You can create categories, add tags, and quickly access your prompt collection for repeated use."}, {"title": "Do I need prompt engineering experience to use this tool?", "description": "Not at all! Our AI prompt generator is designed for everyone, from beginners to experts. The tool handles all the complex prompt engineering automatically, making it easy for anyone to create effective AI prompts without technical knowledge."}, {"title": "How fast can I generate optimized prompts?", "description": "Our AI prompt generator creates optimized prompts in seconds. Simply input your requirements, and the tool instantly generates multiple prompt variations using advanced prompt engineering techniques, saving you hours of manual work."}]}, "cta": {"name": "cta", "title": "Try AI Prompt Generator For Free Now", "description": "Join thousands using Prompt Ark for advanced prompt engineering.", "buttons": [{"title": "Try Free Now", "url": "/#generator", "target": "_self", "icon": "RiMagicLine"}, {"title": "Learn More", "url": "/#feature", "target": "_self", "variant": "outline"}]}, "footer": {"name": "footer", "brand": {"title": "Prompt Ark", "description": "Prompt Ark is the ultimate AI prompt generator for advanced prompt engineering. Create better artificial intelligence prompts that deliver superior results.", "logo": {"src": "/logo.png", "alt": "Prompt Ark"}, "url": "/"}, "copyright": "© 2025 • Prompt Ark All rights reserved.", "nav": {"items": [{"title": "Generator", "children": [{"title": "ChatGPT Prompt Generator", "url": "/chatgpt-prompt-generator", "target": "_self"}, {"title": "VEO 3 Prompt Generator", "url": "/veo-3-prompt-generator", "target": "_self"}]}, {"title": "About", "children": [{"title": "Contact", "url": "/contact", "target": "_self"}, {"title": "About Us", "url": "/about", "target": "_self"}, {"title": "Chrome Extension", "url": "https://chromewebstore.google.com/detail/promptark-1-click-ai-prom/gcoohpmfgfaidgnbmghpakfcbmmmffdh", "target": "_self"}, {"title": "<PERSON><PERSON><PERSON>", "url": "https://github.com/comparelists", "target": "_blank"}]}, {"title": "Friends", "children": [{"title": "AI ASMR Generator", "url": "https://ai-asmr-generator.com/", "target": "_blank"}, {"title": "ListCompare", "url": "https://list-compare.net/", "target": "_blank"}, {"title": "<PERSON><PERSON>", "url": "https://mergerotgame.com/", "target": "_blank"}]}]}, "agreement": {"items": [{"title": "Privacy Policy", "url": "/privacy-policy"}, {"title": "Terms of Service", "url": "/terms-of-service"}]}}}